# Fix Migration Issue in Production

Your deployment is failing because of a failed SQLite-to-PostgreSQL migration. Here's how to fix it:

## Problem
The old migration used SQLite syntax (`DATETIME`) which doesn't exist in PostgreSQL. The migration failed and is now blocking new deployments.

## Solution

### Step 1: Reset the Migration State

Connect to your PostgreSQL database and run these commands to reset the migration state:

```sql
-- Connect to your database
\c direct_checkout_links;

-- Check if _prisma_migrations table exists
\dt _prisma_migrations

-- If it exists, drop the failed migration record
DELETE FROM "_prisma_migrations" WHERE migration_name = '20240530213853_create_session_table';

-- If the table doesn't exist, create it
CREATE TABLE "_prisma_migrations" (
    "id" VARCHAR(36) NOT NULL,
    "checksum" VARCHAR(64) NOT NULL,
    "finished_at" TIMESTAMPTZ,
    "migration_name" VARCHAR(255) NOT NULL,
    "logs" TEXT,
    "rolled_back_at" TIMESTAMPTZ,
    "started_at" TIMESTAMPTZ NOT NULL DEFAULT now(),
    "applied_steps_count" INTEGER NOT NULL DEFAULT 0,
    CONSTRAINT "_prisma_migrations_pkey" PRIMARY KEY ("id")
);
```

### Step 2: Clean Up Any Partial Tables

If any tables were partially created, drop them:

```sql
-- Drop any tables that might have been partially created
DROP TABLE IF EXISTS "Session" CASCADE;
DROP TABLE IF EXISTS "auto_checkout_settings" CASCADE;
DROP TABLE IF EXISTS "generated_links" CASCADE;
DROP TABLE IF EXISTS "link_analytics" CASCADE;
```

### Step 3: Update Your Deployment

Your app now has the correct PostgreSQL migration file. Redeploy the app and it should work.

## Alternative: Manual Table Creation

If migrations still fail, create tables manually:

```sql
-- Connect to your database
\c direct_checkout_links;

-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "isOnline" BOOLEAN NOT NULL DEFAULT false,
    "scope" TEXT,
    "expires" TIMESTAMP(3),
    "accessToken" TEXT NOT NULL,
    "userId" BIGINT,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "accountOwner" BOOLEAN NOT NULL DEFAULT false,
    "locale" TEXT,
    "collaborator" BOOLEAN DEFAULT false,
    "emailVerified" BOOLEAN DEFAULT false,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "auto_checkout_settings" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "auto_checkout_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "generated_links" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "variantId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "checkoutUrl" TEXT NOT NULL,
    "cartUrl" TEXT NOT NULL,
    "directProductUrl" TEXT,
    "advancedDirectUrl" TEXT,
    "productTitle" TEXT,
    "variantTitle" TEXT,
    "price" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "generated_links_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "link_analytics" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "linkId" TEXT,
    "linkType" TEXT NOT NULL,
    "productId" TEXT,
    "variantId" TEXT,
    "clicked" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userAgent" TEXT,
    "referer" TEXT,
    "ipAddress" TEXT,

    CONSTRAINT "link_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "auto_checkout_settings_shop_key" ON "auto_checkout_settings"("shop");

-- CreateIndex
CREATE INDEX "generated_links_shop_createdAt_idx" ON "generated_links"("shop", "createdAt");

-- CreateIndex
CREATE INDEX "generated_links_shop_productId_idx" ON "generated_links"("shop", "productId");

-- CreateIndex
CREATE INDEX "link_analytics_shop_clicked_idx" ON "link_analytics"("shop", "clicked");

-- CreateIndex
CREATE INDEX "link_analytics_linkType_clicked_idx" ON "link_analytics"("linkType", "clicked");

-- Mark migration as applied
INSERT INTO "_prisma_migrations" (
    "id", 
    "checksum", 
    "finished_at", 
    "migration_name", 
    "logs", 
    "applied_steps_count"
) VALUES (
    gen_random_uuid()::text,
    'manual_postgresql_init',
    NOW(),
    '20240531092300_postgresql_init',
    'Tables created manually',
    1
);
```

### Step 4: Skip Database Setup (Alternative)

If you want to deploy without database setup temporarily, modify `package.json`:

```json
{
  "scripts": {
    "docker-start": "npm run start",
    "setup": "echo 'Skipping database setup - using manual tables'"
  }
}
```

## What Was Fixed

✅ **DATETIME → TIMESTAMP(3)** - PostgreSQL compatible date type  
✅ **PRIMARY KEY syntax** - Proper PostgreSQL constraint syntax  
✅ **All tables included** - Session, auto_checkout_settings, generated_links, link_analytics  
✅ **Indexes created** - Performance optimization indexes  
✅ **Migration tracking** - Proper _prisma_migrations entries  

After following these steps, your deployment should succeed! 🚀 