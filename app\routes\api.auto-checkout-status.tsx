import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const { admin } = await authenticate.admin(request);

    // Get the current auto-checkout setting from app metafields
    const response = await admin.graphql(
      `#graphql
        query getCurrentApp {
          currentAppInstallation {
            id
            metafields(first: 10) {
              edges {
                node {
                  namespace
                  key
                  value
                }
              }
            }
          }
        }`
    );

    const responseJson = await response.json();
    const metafields = responseJson.data?.currentAppInstallation?.metafields?.edges || [];
    
    // Look for our auto-checkout setting
    const autoCheckoutMetafield = metafields.find(
      (edge: any) => edge.node.namespace === "direct-checkout" && edge.node.key === "enabled"
    );
    
    const autoCheckoutEnabled = autoCheckoutMetafield?.node?.value === "true";

    return json({ 
      enabled: autoCheckoutEnabled,
      status: "success" 
    }, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("Error checking auto-checkout status:", error);
    return json({ 
      enabled: false,
      status: "error",
      message: "Failed to check auto-checkout status"
    }, {
      status: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  }
}; 