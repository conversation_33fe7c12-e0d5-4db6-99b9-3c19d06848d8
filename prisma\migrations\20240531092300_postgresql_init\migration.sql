-- CreateTable
CREATE TABLE "Session" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "state" TEXT NOT NULL,
    "isOnline" BOOLEAN NOT NULL DEFAULT false,
    "scope" TEXT,
    "expires" TIMESTAMP(3),
    "accessToken" TEXT NOT NULL,
    "userId" BIGINT,
    "firstName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "accountOwner" BOOLEAN NOT NULL DEFAULT false,
    "locale" TEXT,
    "collaborator" BOOLEAN DEFAULT false,
    "emailVerified" BOOLEAN DEFAULT false,

    CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "auto_checkout_settings" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "auto_checkout_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "generated_links" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "variantId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "checkoutUrl" TEXT NOT NULL,
    "cartUrl" TEXT NOT NULL,
    "directProductUrl" TEXT,
    "advancedDirectUrl" TEXT,
    "productTitle" TEXT,
    "variantTitle" TEXT,
    "price" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "generated_links_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "link_analytics" (
    "id" TEXT NOT NULL,
    "shop" TEXT NOT NULL,
    "linkId" TEXT,
    "linkType" TEXT NOT NULL,
    "productId" TEXT,
    "variantId" TEXT,
    "clicked" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userAgent" TEXT,
    "referer" TEXT,
    "ipAddress" TEXT,

    CONSTRAINT "link_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "auto_checkout_settings_shop_key" ON "auto_checkout_settings"("shop");

-- CreateIndex
CREATE INDEX "generated_links_shop_createdAt_idx" ON "generated_links"("shop", "createdAt");

-- CreateIndex
CREATE INDEX "generated_links_shop_productId_idx" ON "generated_links"("shop", "productId");

-- CreateIndex
CREATE INDEX "link_analytics_shop_clicked_idx" ON "link_analytics"("shop", "clicked");

-- CreateIndex
CREATE INDEX "link_analytics_linkType_clicked_idx" ON "link_analytics"("linkType", "clicked"); 