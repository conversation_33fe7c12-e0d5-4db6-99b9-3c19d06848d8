# Products Scanner Feature

## Overview
The Products Scanner is a powerful tool that helps identify single-variant products in your Shopify store that are perfect for direct checkout links and Facebook advertising.

## What it Does

### 🔍 **Product Analysis**
- Scans all products in your store
- Identifies products with only one variant (no size/color/options)
- Filters for active products only
- Shows inventory levels and pricing

### 📊 **Smart Filtering**
Products are included if they have:
- Exactly 1 variant
- No custom options (only "Default Title")
- Active status
- Available inventory

### 📈 **Facebook Ads Integration**
Export product data in multiple formats:

#### **1. Facebook Catalog Format (.tsv)**
- Ready-to-upload tab-separated file
- Includes: ID, Title, Price, Image, URL, Category, Brand
- Perfect for Facebook Product Catalog

#### **2. Product IDs Only (.txt)**
- Clean list of Shopify product IDs
- One ID per line
- Great for Custom Audiences

#### **3. Product URLs (.txt)**
- Direct product page URLs
- Perfect for Dynamic Product Ads
- Works with retargeting campaigns

## How to Use

### Step 1: Scan Products
1. Go to **Products Scanner** in your app navigation
2. Click **"Scan Products"** button
3. Wait for analysis to complete

### Step 2: Review Results
- See total products vs. single-variant products
- Browse the filtered list with images, prices, inventory
- Check product types and vendors

### Step 3: Select Products
- Use checkboxes to select specific products
- Or click **"Select All"** for bulk selection
- Selection counter shows how many are chosen

### Step 4: Export for Facebook
1. Choose export format:
   - **Facebook Catalog** - Full product data for catalog upload
   - **Product IDs** - For custom audiences and tracking
   - **Product URLs** - For dynamic ads and retargeting

2. Click **"Generate Export"**
3. **Download** file or **Copy to Clipboard**

## Facebook Ads Setup

### Using Facebook Catalog Format:
1. Export as "Facebook Catalog Format"
2. In Facebook Business Manager → Catalog Manager
3. Upload the .tsv file
4. Create Dynamic Product Ads campaigns

### Using Product IDs:
1. Export as "Product IDs Only"
2. Create Custom Audiences in Facebook Ads Manager
3. Use for retargeting and lookalike audiences

### Using Product URLs:
1. Export as "Product URLs"
2. Set up Dynamic Product Ads
3. Use URLs for pixel tracking and retargeting

## Why Single-Variant Products?

### ✅ **Perfect for Direct Checkout**
- No variant selection needed
- Customers can buy immediately
- Higher conversion rates
- Faster checkout process

### ✅ **Better for Facebook Ads**
- Direct product links work perfectly
- No confusion about options
- Clear product messaging
- Easier to track conversions

### ✅ **Improved User Experience**
- One-click purchasing
- Reduced cart abandonment
- Mobile-optimized flow
- Streamlined buying process

## Technical Notes

- Scans up to 250 products per batch
- Filters based on Shopify's variant structure
- Exports use standard Facebook formats
- Compatible with all Facebook advertising tools

## Best Practices

1. **Regular Scanning**: Run monthly to catch new single-variant products
2. **Inventory Check**: Ensure selected products have stock before advertising
3. **Price Updates**: Re-export when prices change
4. **Category Grouping**: Group similar products for targeted campaigns
5. **Performance Tracking**: Monitor which products convert best

This feature is designed to maximize the effectiveness of your direct checkout links while providing seamless integration with Facebook's advertising ecosystem. 