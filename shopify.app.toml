# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "c02bbd802d7d390e81157781566fd50f"
name = "Direct Checkout Links"
handle = "direct-checkout-links"
application_url = "https://direct-checkout-url-direct-checkout-github.4dieky.easypanel.host"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_products,unauthenticated_read_product_listings,unauthenticated_write_checkouts"

[auth]
redirect_urls = [
  "https://direct-checkout-url-direct-checkout-github.4dieky.easypanel.host/auth/callback",
  "https://direct-checkout-url-direct-checkout-github.4dieky.easypanel.host/auth/shopify/callback",
  "https://direct-checkout-url-direct-checkout-github.4dieky.easypanel.host/api/auth/callback"
]

[pos]
embedded = false
