import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useSubmit, useActionData } from "@remix-run/react";
import {
  Card,
  Layout,
  Page,
  Text,
  Button,
  BlockStack,
  InlineStack,
  <PERSON>ge,
  Di<PERSON>r,
  Banner,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useEffect } from "react";
import { authenticate } from "../shopify.server";
import { SUBSCRIPTION_CONFIG } from "../config/subscription";

// Use direct Prisma calls instead of the billing service for now
import prisma from "../db.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  
  // Check for success and error parameters
  const url = new URL(request.url);
  const success = url.searchParams.get("success");
  const error = url.searchParams.get("error");

  try {
    // Get subscription directly from database
    const subscription = await (prisma as any).subscription.findUnique({
      where: { shop: session.shop }
    });

    console.log("Billing loader - Shop:", session.shop);
    console.log("Billing loader - Subscription found:", subscription);
    console.log("Billing loader - Success param:", success);

    // Calculate status
    let status = 'none';
    let daysLeft = 0;
    let message = '';
    let hasAccess = false;
    let showSuccessMessage = success === "true";
    let showErrorMessage = false;
    let errorMessage = '';

    // Handle callback errors
    if (error) {
      showErrorMessage = true;
      switch (error) {
        case 'callback_failed':
          errorMessage = 'Failed to activate subscription. Please try again or contact support.';
          break;
        case 'no_charge_id':
          errorMessage = 'Invalid subscription callback. Please try subscribing again.';
          break;
        default:
          errorMessage = 'An error occurred with your subscription. Please try again.';
      }
    }

    if (!subscription) {
      // New user - create trial
      const trialEndsAt = new Date();
      trialEndsAt.setDate(trialEndsAt.getDate() + SUBSCRIPTION_CONFIG.trialDays);
      
      const newSubscription = await (prisma as any).subscription.create({
        data: {
          shop: session.shop,
          status: 'trial',
          trialEndsAt,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log("Billing loader - Created new trial:", newSubscription);

      status = 'trial';
      daysLeft = SUBSCRIPTION_CONFIG.trialDays;
      message = `Welcome! You have ${SUBSCRIPTION_CONFIG.trialDays} days of free trial.`;
      hasAccess = true;
    } else {
      const now = new Date();
      
      switch (subscription.status) {
        case 'trial':
          if (subscription.trialEndsAt && now <= subscription.trialEndsAt) {
            daysLeft = Math.ceil((subscription.trialEndsAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
            status = 'trial';
            message = daysLeft > 1 ? `${daysLeft} days left in trial` : 'Trial ends today';
            hasAccess = true;
          } else {
            status = 'expired';
            message = 'Trial expired. Please subscribe to continue.';
            hasAccess = false;
          }
          break;
        case 'active':
          status = 'active';
          message = showSuccessMessage ? 'Subscription activated successfully! 🎉' : 'Subscription active';
          hasAccess = true;
          break;
        default:
          status = subscription.status;
          message = 'Subscription required to use this app.';
          hasAccess = false;
      }
    }

    return json({
      subscription: {
        status,
        daysLeft,
        message,
        hasAccess,
        showSuccessMessage,
        showErrorMessage,
        errorMessage
      },
      config: SUBSCRIPTION_CONFIG
    });
  } catch (error) {
    console.error("Error loading billing data:", error);
    return json({
      subscription: {
        status: 'error',
        daysLeft: 0,
        message: 'Error loading subscription data',
        hasAccess: false,
        showSuccessMessage: false,
        showErrorMessage: false,
        errorMessage: ''
      },
      config: SUBSCRIPTION_CONFIG
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "subscribe") {
    try {
      // CRITICAL: Check for existing active subscription to prevent double charging
      const existingSubscription = await (prisma as any).subscription.findUnique({
        where: { shop: session.shop }
      });

      if (existingSubscription && existingSubscription.status === 'active') {
        return json({ 
          error: "You already have an active subscription. Cannot create duplicate subscriptions." 
        }, { status: 400 });
      }

      // Check for pending subscriptions too
      if (existingSubscription && existingSubscription.status === 'pending') {
        return json({ 
          error: "You have a pending subscription. Please complete or cancel the existing one first." 
        }, { status: 400 });
      }

      // Query existing Shopify subscriptions to double-check
      const existingSubscriptionsResponse = await admin.graphql(
        `#graphql
          query appSubscriptions($first: Int!) {
            currentAppInstallation {
              activeSubscriptions(first: $first) {
                edges {
                  node {
                    id
                    status
                    name
                    lineItems {
                      plan {
                        pricingDetails {
                          ... on AppRecurringPricing {
                            price {
                              amount
                              currencyCode
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }`,
        {
          variables: { first: 10 }
        }
      );

      const existingSubscriptionsJson = await existingSubscriptionsResponse.json();
      const activeSubscriptions = existingSubscriptionsJson.data?.currentAppInstallation?.activeSubscriptions?.edges || [];

      if (activeSubscriptions.length > 0) {
        console.log("Found existing active Shopify subscriptions:", activeSubscriptions);
        return json({ 
          error: "You already have active subscriptions in Shopify. Cannot create duplicate subscriptions." 
        }, { status: 400 });
      }

      const response = await admin.graphql(
        `#graphql
          mutation appSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!) {
            appSubscriptionCreate(name: $name, lineItems: $lineItems, returnUrl: $returnUrl) {
              appSubscription {
                id
                status
              }
              confirmationUrl
              userErrors {
                field
                message
              }
            }
          }`,
        {
          variables: {
            name: SUBSCRIPTION_CONFIG.planName,
            returnUrl: `${process.env.SHOPIFY_APP_URL}/app/billing/callback`,
            lineItems: [
              {
                plan: {
                  appRecurringPricingDetails: {
                    price: { amount: SUBSCRIPTION_CONFIG.monthlyPrice.toString(), currencyCode: "USD" }
                  }
                }
              }
            ]
          }
        }
      );

      const responseJson = await response.json();
      
      if (responseJson.data?.appSubscriptionCreate?.userErrors?.length > 0) {
        throw new Error(responseJson.data.appSubscriptionCreate.userErrors[0].message);
      }

      const subscription = responseJson.data?.appSubscriptionCreate?.appSubscription;
      const confirmationUrl = responseJson.data?.appSubscriptionCreate?.confirmationUrl;

      if (subscription?.id && confirmationUrl) {
        // Update subscription in database
        await (prisma as any).subscription.upsert({
          where: { shop: session.shop },
          update: {
            subscriptionId: subscription.id,
            status: 'pending',
            updatedAt: new Date()
          },
          create: {
            shop: session.shop,
            subscriptionId: subscription.id,
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });

        console.log("Created new subscription:", subscription.id, "for shop:", session.shop);

        // Return the confirmation URL instead of redirecting
        return json({ confirmationUrl });
      }
    } catch (error) {
      console.error("Error creating subscription:", error);
      return json({ error: "Failed to create subscription" }, { status: 500 });
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
};

export default function Billing() {
  const { subscription, config } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();

  // Handle confirmation URL redirect using App Bridge
  useEffect(() => {
    if (actionData && 'confirmationUrl' in actionData) {
      window.parent.location.href = actionData.confirmationUrl;
    }
  }, [actionData]);

  const handleSubscribe = () => {
    submit({ action: "subscribe" }, { method: "post" });
  };

  const getStatusBadge = () => {
    switch (subscription.status) {
      case 'trial':
        return <Badge tone="attention">Free Trial</Badge>;
      case 'active':
        return <Badge tone="success">Active</Badge>;
      case 'expired':
        return <Badge tone="critical">Expired</Badge>;
      case 'cancelled':
        return <Badge tone="critical">Cancelled</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  // Determine if subscribe button should be shown
  const shouldShowSubscribeButton = () => {
    return subscription.status === 'expired' || 
           subscription.status === 'cancelled' || 
           subscription.status === 'none' ||
           subscription.status === 'error';
  };

  const shouldShowEarlySubscribeButton = () => {
    return subscription.status === 'trial' && subscription.hasAccess;
  };

  return (
    <Page>
      <TitleBar title="Billing & Subscription" />
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <InlineStack align="space-between">
                <Text as="h3" variant="headingMd">Subscription Status</Text>
                {getStatusBadge()}
              </InlineStack>
              
              <Text as="p">{subscription.message}</Text>

              {/* Show error message from action if any */}
              {actionData && 'error' in actionData && (
                <Banner tone="critical">
                  <Text as="p">{actionData.error}</Text>
                </Banner>
              )}

              {/* Show callback error messages */}
              {subscription.showErrorMessage && (
                <Banner tone="critical">
                  <Text as="p">{subscription.errorMessage}</Text>
                </Banner>
              )}

              {subscription.showSuccessMessage && subscription.status === 'active' && (
                <Banner tone="success">
                  <Text as="p">
                    🎉 Subscription activated successfully! You now have full access to all premium features.
                  </Text>
                </Banner>
              )}

              {subscription.status === 'trial' && subscription.daysLeft > 0 && (
                <Banner tone="info">
                  <Text as="p">
                    Your free trial expires in {subscription.daysLeft} day{subscription.daysLeft !== 1 ? 's' : ''}. 
                    Subscribe now to continue using the app without interruption.
                  </Text>
                </Banner>
              )}

              {(subscription.status === 'expired' || subscription.status === 'cancelled') && (
                <Banner tone="warning">
                  <Text as="p">
                    Your subscription has {subscription.status}. Subscribe to regain access to all app features.
                  </Text>
                </Banner>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">{config.planName}</Text>
              
              <InlineStack align="space-between">
                <Text as="h2" variant="headingLg">${config.monthlyPrice.toString()}/month</Text>
                <Text as="span" tone="subdued">USD</Text>
              </InlineStack>

              <Divider />

              <BlockStack gap="200">
                <Text as="h4" variant="headingSm">Features included:</Text>
                <Text as="p">• Generate unlimited direct checkout links</Text>
                <Text as="p">• Auto-checkout functionality</Text>
                <Text as="p">• Advanced link customization</Text>
                <Text as="p">• Link analytics and tracking</Text>
                <Text as="p">• Global auto-checkout toggle</Text>
                <Text as="p">• Premium support</Text>
              </BlockStack>

              <Divider />

              <BlockStack gap="200">
                <Text as="h4" variant="headingSm">Trial Information:</Text>
                <Text as="p">• {config.trialDays} days free trial</Text>
                <Text as="p">• No credit card required</Text>
                <Text as="p">• Full access to all features</Text>
                <Text as="p">• Cancel anytime</Text>
              </BlockStack>

              {/* Only show subscribe button for expired/cancelled/none/error statuses */}
              {shouldShowSubscribeButton() && (
                <Button
                  variant="primary"
                  size="large"
                  onClick={handleSubscribe}
                >
                  Subscribe Now - ${config.monthlyPrice.toString()}/month
                </Button>
              )}

              {/* Show early subscribe button only for active trials */}
              {shouldShowEarlySubscribeButton() && (
                <Button
                  variant="primary"
                  tone="success"
                  size="large"
                  onClick={handleSubscribe}
                >
                  Subscribe Early - ${config.monthlyPrice.toString()}/month
                </Button>
              )}

              {/* Show active subscription status */}
              {subscription.status === 'active' && (
                <BlockStack gap="200">
                  <Text as="p" tone="success">
                    ✅ You're all set! Your subscription is active.
                  </Text>
                  <Text as="p" tone="subdued">
                    Your subscription will automatically renew each month. You can manage or cancel your subscription through your Shopify admin panel.
                  </Text>
                </BlockStack>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
} 