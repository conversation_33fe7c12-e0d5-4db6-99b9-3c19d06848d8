import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import crypto from "crypto";
import prisma from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Verify webhook authenticity
    const body = await request.text();
    const hmacHeader = request.headers.get("X-Shopify-Hmac-Sha256");
    
    if (!hmacHeader) {
      console.error("Missing HMAC header for shop/redact webhook");
      return json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify HMAC
    const hash = crypto
      .createHmac("sha256", process.env.SHOPIFY_WEBHOOK_SECRET || "")
      .update(body, "utf8")
      .digest("base64");

    if (hash !== hmacHeader) {
      console.error("Invalid HMAC for shop/redact webhook");
      return json({ error: "Unauthorized" }, { status: 401 });
    }

    const payload = JSON.parse(body);
    console.log("Received shop/redact webhook:", payload);

    const { shop_id, shop_domain } = payload;
    
    console.log("Shop uninstall redaction for:", {
      shop_id,
      shop_domain,
      timestamp: new Date().toISOString()
    });

    // Delete all shop data from our database
    try {
      // Delete shop-specific data in correct order (foreign key constraints)
      
      // 1. Delete link analytics
      await (prisma as any).linkAnalytics.deleteMany({
        where: { shop: shop_domain }
      });

      // 2. Delete generated links
      await (prisma as any).generatedLink.deleteMany({
        where: { shop: shop_domain }
      });

      // 3. Delete auto-checkout settings
      await (prisma as any).autoCheckoutSettings.deleteMany({
        where: { shop: shop_domain }
      });

      // 4. Delete subscription data
      await (prisma as any).subscription.deleteMany({
        where: { shop: shop_domain }
      });

      // 5. Delete sessions
      await (prisma as any).session.deleteMany({
        where: { shop: shop_domain }
      });

      console.log("Successfully deleted all data for shop:", shop_domain);

      const response = {
        status: "completed",
        message: "Shop data redaction completed successfully",
        shop_domain,
        shop_id,
        deleted_data: [
          "Auto-checkout settings",
          "Generated checkout links",
          "Link analytics",
          "Subscription data",
          "Session data"
        ],
        timestamp: new Date().toISOString()
      };

      return json(response, { status: 200 });

    } catch (dbError) {
      console.error("Database error during shop redaction:", dbError);
      
      // Still return 200 to acknowledge receipt, but log the error
      return json({
        status: "error",
        message: "Error occurred during data deletion",
        shop_domain,
        error: "Database operation failed"
      }, { status: 200 });
    }

  } catch (error) {
    console.error("Error processing shop/redact webhook:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};