import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    // For now, we'll return enabled: true since we can't access app metafields without authentication
    // A better approach would be to store this setting in a way that's publicly accessible
    // or handle the check differently
    
    // Extract shop domain from the request
    const url = new URL(request.url);
    const shopDomain = request.headers.get('x-shopify-shop-domain') || 
                      request.headers.get('referer')?.split('//')[1]?.split('/')[0] ||
                      url.searchParams.get('shop');

    // For now, return enabled: true to make the feature work
    // In a production app, you might want to store this setting in a different way
    return json({ 
      enabled: true, // Always enabled for simplicity
      status: "success",
      shop: shopDomain
    }, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("Error checking auto-checkout status:", error);
    return json({ 
      enabled: false,
      status: "error",
      message: "Failed to check auto-checkout status"
    }, {
      status: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  }
}; 