import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  
  const url = new URL(request.url);
  const chargeId = url.searchParams.get("charge_id");
  
  console.log("Billing callback - Shop:", session.shop, "Charge ID:", chargeId);
  
  if (chargeId) {
    try {
      // Update subscription status to active
      const updatedSubscription = await (prisma as any).subscription.upsert({
        where: { shop: session.shop },
        update: {
          chargeId,
          status: 'active',
          updatedAt: new Date()
        },
        create: {
          shop: session.shop,
          chargeId,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log("Billing callback - Updated subscription:", updatedSubscription);
    } catch (error) {
      console.error("Error updating subscription after callback:", error);
      // Redirect to billing page with error parameter
      return redirect("/app/billing?error=callback_failed");
    }
  } else {
    console.warn("Billing callback - No charge_id provided");
    return redirect("/app/billing?error=no_charge_id");
  }

  // Redirect back to billing page with success
  return redirect("/app/billing?success=true");
}; 