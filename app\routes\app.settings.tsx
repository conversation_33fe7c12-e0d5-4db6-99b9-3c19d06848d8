import { useEffect, useState, useCallback } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useFetcher, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Frame,
  Toast,
  Banner,
  InlineStack,
  Badge,
  Divider,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { getAutoCheckoutSetting, setAutoCheckoutSetting } from "../services/autoCheckout.server";

interface SettingsData {
  autoCheckoutEnabled: boolean;
}

interface ActionData {
  success: boolean;
  error?: string;
  autoCheckoutEnabled?: boolean;
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);

  try {
    const autoCheckoutEnabled = await getAutoCheckoutSetting(session.shop);
    return json({ autoCheckoutEnabled });
  } catch (error) {
    console.error("Error loading settings:", error);
    return json({ autoCheckoutEnabled: false });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const actionType = formData.get("action");

  if (actionType === "toggleAutoCheckout") {
    const enabled = formData.get("enabled") === "true";

    try {
      const success = await setAutoCheckoutSetting(session.shop, enabled);
      
      if (!success) {
        throw new Error("Failed to update auto-checkout setting");
      }

      return json({
        success: true,
        autoCheckoutEnabled: enabled,
      } as ActionData);
    } catch (error) {
      return json({
        success: false,
        error: error instanceof Error ? error.message : "Failed to update settings",
      } as ActionData);
    }
  }

  return json({ success: false, error: "Invalid action" } as ActionData);
};

export default function Settings() {
  const { autoCheckoutEnabled } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<ActionData>();
  
  const [currentlyEnabled, setCurrentlyEnabled] = useState(autoCheckoutEnabled);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const toggleAutoCheckout = useCallback(() => {
    const newValue = !currentlyEnabled;
    
    const formData = new FormData();
    formData.append("action", "toggleAutoCheckout");
    formData.append("enabled", newValue.toString());

    fetcher.submit(formData, { method: "POST" });
  }, [currentlyEnabled, fetcher]);

  useEffect(() => {
    if (fetcher.data?.success) {
      setCurrentlyEnabled(fetcher.data.autoCheckoutEnabled || false);
      setToastMessage(
        fetcher.data.autoCheckoutEnabled 
          ? "Auto-checkout enabled globally!" 
          : "Auto-checkout disabled globally!"
      );
      setShowToast(true);
    } else if (fetcher.data?.error) {
      setToastMessage(fetcher.data.error);
      setShowToast(true);
    }
  }, [fetcher.data]);

  const isLoading = ["loading", "submitting"].includes(fetcher.state);

  return (
    <Frame>
      <Page>
        <TitleBar title="Auto-Checkout Settings" />
        <BlockStack gap="500">
          <Layout>
            <Layout.Section>
              <Banner
                title={currentlyEnabled ? "Auto-Checkout is Enabled" : "Auto-Checkout is Disabled"}
                tone={currentlyEnabled ? "success" : "info"}
              >
                <p>
                  {currentlyEnabled 
                    ? "Customers visiting any product page with &direct-checkout=true will be automatically redirected to checkout."
                    : "Auto-checkout functionality is currently disabled. Customers will see normal product pages even with the direct-checkout parameter."
                  }
                </p>
              </Banner>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="500">
                  <InlineStack align="space-between" blockAlign="center">
                    <BlockStack gap="200">
                      <Text as="h2" variant="headingMd">
                        Global Auto-Checkout Toggle
                      </Text>
                      <Text variant="bodyMd" tone="subdued" as="p">
                        Enable or disable auto-checkout functionality across all products
                      </Text>
                    </BlockStack>
                    
                    <InlineStack gap="300" align="center">
                      <Badge tone={currentlyEnabled ? "success" : "info"}>
                        {currentlyEnabled ? "Enabled" : "Disabled"}
                      </Badge>
                      
                      <Button
                        variant={currentlyEnabled ? "primary" : "secondary"}
                        tone={currentlyEnabled ? "critical" : "success"}
                        loading={isLoading}
                        onClick={toggleAutoCheckout}
                      >
                        {currentlyEnabled ? "Disable Auto-Checkout" : "Enable Auto-Checkout"}
                      </Button>
                    </InlineStack>
                  </InlineStack>
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text as="h3" variant="headingSm">
                    How It Works
                  </Text>
                  
                  <BlockStack gap="300">
                    <Text variant="bodyMd" as="p">
                      <strong>When Enabled:</strong> Any product URL with <code>?direct-checkout=true</code> will automatically add the product to cart and redirect to checkout.
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      <strong>When Disabled:</strong> The <code>?direct-checkout=true</code> parameter is ignored, and customers see the normal product page.
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      <strong>Simple URL Format:</strong> <code>yourstore.com/products/any-product?direct-checkout=true</code>
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      <strong>Advanced Format:</strong> <code>yourstore.com/products/any-product?direct-checkout=true&variant=12345&quantity=2</code>
                    </Text>
                  </BlockStack>
                  
                  <Divider />
                  
                  <BlockStack gap="300">
                    <Text as="h4" variant="headingSm">
                      Requirements
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      ✅ Theme code must be installed (see Theme Setup page)
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      ✅ Valid product URL (variant and quantity are optional)
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      ✅ This global setting must be enabled
                    </Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text as="h3" variant="headingSm">
                    Example Usage
                  </Text>
                  
                  <Text variant="bodyMd" as="p">
                    With auto-checkout enabled, you can create links manually or use them in:
                  </Text>
                  
                  <BlockStack gap="200">
                    <Text variant="bodyMd" as="p">
                      • <strong>Email campaigns:</strong> Direct product-to-checkout links
                    </Text>
                    <Text variant="bodyMd" as="p">
                      • <strong>Social media:</strong> Skip product browsing, go straight to purchase
                    </Text>
                    <Text variant="bodyMd" as="p">
                      • <strong>Advertising:</strong> Reduce friction in ad-to-purchase flow
                    </Text>
                    <Text variant="bodyMd" as="p">
                      • <strong>Partner sites:</strong> Seamless product referrals
                    </Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </Layout.Section>
          </Layout>
        </BlockStack>
        
        {showToast && (
          <Toast
            content={toastMessage}
            onDismiss={() => setShowToast(false)}
          />
        )}
      </Page>
    </Frame>
  );
} 