import prisma from "../db.server";

export async function getAutoCheckoutSetting(shop: string): Promise<boolean> {
  try {
    const setting = await prisma.autoCheckoutSettings.findUnique({
      where: { shop },
    });
    
    return setting?.enabled ?? false;
  } catch (error) {
    console.error("Error getting auto-checkout setting:", error);
    return false;
  }
}

export async function setAutoCheckoutSetting(shop: string, enabled: boolean): Promise<boolean> {
  try {
    await prisma.autoCheckoutSettings.upsert({
      where: { shop },
      update: { enabled },
      create: { shop, enabled },
    });
    
    return true;
  } catch (error) {
    console.error("Error setting auto-checkout setting:", error);
    return false;
  }
}

export async function saveGeneratedLink(data: {
  shop: string;
  productId: string;
  variantId: string;
  quantity: number;
  checkoutUrl: string;
  cartUrl: string;
  directProductUrl?: string;
  advancedDirectUrl?: string;
  productTitle?: string;
  variantTitle?: string;
  price?: string;
}) {
  try {
    const link = await prisma.generatedLink.create({
      data,
    });
    
    return link;
  } catch (error) {
    console.error("Error saving generated link:", error);
    return null;
  }
}

export async function getGeneratedLinks(shop: string, limit: number = 50) {
  try {
    const links = await prisma.generatedLink.findMany({
      where: { shop },
      orderBy: { createdAt: "desc" },
      take: limit,
    });
    
    return links;
  } catch (error) {
    console.error("Error getting generated links:", error);
    return [];
  }
}

export async function recordLinkClick(data: {
  shop: string;
  linkId?: string;
  linkType: string;
  productId?: string;
  variantId?: string;
  userAgent?: string;
  referer?: string;
  ipAddress?: string;
}) {
  try {
    const analytics = await prisma.linkAnalytics.create({
      data,
    });
    
    return analytics;
  } catch (error) {
    console.error("Error recording link click:", error);
    return null;
  }
} 