import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import {
  Card,
  Layout,
  Page,
  Text,
  Button,
  BlockStack,
  InlineStack,
  Badge,
  DataTable,
  Thumbnail,
  Banner,
  Spinner,
  TextField,
  Select,
  Checkbox,
} from "@shopify/polaris";
import { useState, useCallback, useEffect } from "react";
import { authenticate } from "../shopify.server";

interface SingleVariantProduct {
  id: string;
  title: string;
  handle: string;
  vendor: string;
  productType: string;
  status: string;
  totalInventory: number;
  variants: {
    edges: Array<{
      node: {
        id: string;
        price: string;
        compareAtPrice?: string;
        inventoryQuantity: number;
        sku?: string;
      }
    }>
  };
  images: {
    edges: Array<{
      node: {
        url: string;
        altText?: string;
      }
    }>
  };
  onlineStoreUrl?: string;
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);
  
  const url = new URL(request.url);
  const scan = url.searchParams.get("scan");
  
  console.log("Products Scanner Loader - scan parameter:", scan);
  
  if (scan !== "true") {
    return json({ 
      products: [] as SingleVariantProduct[], 
      isScanning: false, 
      totalProducts: 0,
      singleVariantCount: 0,
      scanRequested: false
    });
  }

  console.log("Products Scanner - Starting scan...");

  try {
    // Query all products with variants
    const response = await admin.graphql(
      `#graphql
        query getProducts($first: Int!, $after: String) {
          products(first: $first, after: $after) {
            edges {
              node {
                id
                title
                handle
                vendor
                productType
                status
                totalInventory
                variants(first: 10) {
                  edges {
                    node {
                      id
                      price
                      compareAtPrice
                      inventoryQuantity
                      sku
                      selectedOptions {
                        name
                        value
                      }
                    }
                  }
                }
                images(first: 1) {
                  edges {
                    node {
                      url
                      altText
                    }
                  }
                }
                onlineStoreUrl
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }`,
      {
        variables: {
          first: 250,
          after: null
        }
      }
    );

    const responseJson = await response.json();
    console.log("GraphQL Response status:", response.status);
    
    if (!responseJson.data?.products) {
      console.error("No products data in response:", responseJson);
      throw new Error("Failed to fetch products");
    }

    let allProducts: any[] = [];
    let products = responseJson.data.products;
    
    // Collect all products (handle pagination)
    allProducts.push(...products.edges);
    console.log("Total products fetched:", allProducts.length);
    
    // Filter for single variant products (where variant count = 1 AND no custom options)
    const singleVariantProducts = allProducts
      .map(edge => edge.node)
      .filter(product => {
        const variants = product.variants.edges;
        
        // Must have exactly 1 variant
        if (variants.length !== 1) return false;
        
        // Check if the variant has only default options (Title = "Default Title")
        const variant = variants[0].node;
        const hasOnlyDefaultOptions = variant.selectedOptions.length === 1 && 
          variant.selectedOptions[0].name === "Title" && 
          variant.selectedOptions[0].value === "Default Title";
        
        return hasOnlyDefaultOptions && product.status === "ACTIVE";
      });

    console.log("Single variant products found:", singleVariantProducts.length);

    return json({
      products: singleVariantProducts as SingleVariantProduct[],
      isScanning: false,
      totalProducts: allProducts.length,
      singleVariantCount: singleVariantProducts.length,
      scanRequested: true
    });

  } catch (error) {
    console.error("Error scanning products:", error);
    return json({ 
      products: [] as SingleVariantProduct[], 
      isScanning: false, 
      totalProducts: 0,
      singleVariantCount: 0,
      scanRequested: true,
      error: "Failed to scan products: " + (error instanceof Error ? error.message : "Unknown error")
    });
  }
};

export default function ProductsScanner() {
  const { products, isScanning, totalProducts, singleVariantCount, error, scanRequested } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [exportFormat, setExportFormat] = useState("facebook");
  const [showExported, setShowExported] = useState(false);
  const [exportedData, setExportedData] = useState("");
  const [localScanning, setLocalScanning] = useState(false);

  const handleScan = useCallback(() => {
    console.log("Scan button clicked");
    setLocalScanning(true);
    // Use navigate instead of submit
    navigate("/app/products-scanner?scan=true");
  }, [navigate]);

  // Reset local scanning state when scan completes
  useEffect(() => {
    if (scanRequested && !isScanning) {
      setLocalScanning(false);
    }
  }, [scanRequested, isScanning]);

  const handleSelectAll = useCallback(() => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map((p: SingleVariantProduct) => p.id));
    }
  }, [selectedProducts.length, products]);

  const handleProductSelect = useCallback((productId: string) => {
    setSelectedProducts(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  }, []);

  const generateExportData = useCallback(() => {
    const selectedProductsData = products.filter((p: SingleVariantProduct) => 
      selectedProducts.includes(p.id)
    );

    let exportData = "";
    
    switch (exportFormat) {
      case "facebook":
        // Facebook Catalog format
        exportData = selectedProductsData.map((product: SingleVariantProduct) => {
          const variant = product.variants.edges[0].node;
          const image = product.images.edges[0]?.node.url || "";
          
          return [
            product.id.replace('gid://shopify/Product/', ''), // Product ID
            product.title, // Title
            product.handle, // Handle
            variant.price, // Price
            image, // Image URL
            product.onlineStoreUrl || `https://yourstore.myshopify.com/products/${product.handle}`, // Product URL
            product.status, // Availability
            product.productType || "", // Category
            product.vendor || "", // Brand
          ].join('\t');
        }).join('\n');
        
        // Add header
        exportData = "id\ttitle\thandle\tprice\timage_link\tlink\tavailability\tcategory\tbrand\n" + exportData;
        break;
        
      case "ids-only":
        exportData = selectedProductsData
          .map((product: SingleVariantProduct) => product.id.replace('gid://shopify/Product/', ''))
          .join('\n');
        break;
        
      case "urls":
        exportData = selectedProductsData
          .map((product: SingleVariantProduct) => 
            product.onlineStoreUrl || `https://yourstore.myshopify.com/products/${product.handle}`
          )
          .join('\n');
        break;
    }
    
    setExportedData(exportData);
    setShowExported(true);
  }, [selectedProducts, products, exportFormat]);

  const downloadData = useCallback(() => {
    const blob = new Blob([exportedData], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `single-variant-products-${exportFormat}.${exportFormat === 'facebook' ? 'tsv' : 'txt'}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [exportedData, exportFormat]);

  const copyToClipboard = useCallback(() => {
    navigator.clipboard.writeText(exportedData);
  }, [exportedData]);

  // Prepare table data
  const tableRows = products.map((product: SingleVariantProduct) => {
    const variant = product.variants.edges[0].node;
    const image = product.images.edges[0]?.node;
    
    return [
      <InlineStack key={`select-${product.id}`} gap="200">
        <Checkbox
          label=""
          checked={selectedProducts.includes(product.id)}
          onChange={() => handleProductSelect(product.id)}
        />
        {image && (
          <Thumbnail
            source={image.url}
            alt={image.altText || product.title}
            size="small"
          />
        )}
      </InlineStack>,
      <Text key={`title-${product.id}`} as="span" fontWeight="semibold">
        {product.title}
      </Text>,
      <Text key={`price-${product.id}`} as="span">
        ${variant.price}
      </Text>,
      <Badge key={`inventory-${product.id}`} tone={variant.inventoryQuantity > 0 ? "success" : "critical"}>
        {variant.inventoryQuantity} in stock
      </Badge>,
      <Text key={`type-${product.id}`} as="span" tone="subdued">
        {product.productType || "—"}
      </Text>,
      <Text key={`vendor-${product.id}`} as="span" tone="subdued">
        {product.vendor || "—"}
      </Text>
    ];
  });

  const isCurrentlyScanning = isScanning || localScanning;

  return (
    <Page
      title="Products Scanner"
      subtitle="Find single-variant products perfect for direct checkout links"
      primaryAction={{
        content: isCurrentlyScanning ? "Scanning..." : "Scan Products",
        loading: isCurrentlyScanning,
        onAction: handleScan
      }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <Text as="h3" variant="headingMd">
                Single-Variant Product Scanner
              </Text>
              
              <Text as="p">
                This tool scans your store for products with only one variant and no options. 
                These products work best with direct checkout links since customers don't need to select variants.
              </Text>

              {error && (
                <Banner tone="critical">
                  <Text as="p">{error}</Text>
                </Banner>
              )}

              {!isCurrentlyScanning && products.length === 0 && !error && !scanRequested && (
                <Banner tone="info">
                  <Text as="p">
                    Click "Scan Products" to analyze your store and find products suitable for direct checkout links.
                  </Text>
                </Banner>
              )}

              {!isCurrentlyScanning && scanRequested && totalProducts > 0 && !error && (
                <Banner tone="success">
                  <Text as="p">
                    Scan completed! Found {singleVariantCount} single-variant products out of {totalProducts} total products.
                  </Text>
                </Banner>
              )}

              {isCurrentlyScanning && (
                <InlineStack gap="200" align="center">
                  <Spinner size="small" />
                  <Text as="p">Scanning products...</Text>
                </InlineStack>
              )}
            </BlockStack>
          </Card>
        </Layout.Section>

        {products.length > 0 && (
          <>
            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <InlineStack align="space-between">
                    <Text as="h3" variant="headingMd">
                      Single-Variant Products ({products.length})
                    </Text>
                    <InlineStack gap="200">
                      <Button
                        onClick={handleSelectAll}
                        variant="secondary"
                      >
                        {selectedProducts.length === products.length ? "Deselect All" : "Select All"}
                      </Button>
                      <Text as="span">
                        {selectedProducts.length} selected
                      </Text>
                    </InlineStack>
                  </InlineStack>

                  <DataTable
                    columnContentTypes={['text', 'text', 'text', 'text', 'text', 'text']}
                    headings={['Select', 'Product', 'Price', 'Inventory', 'Type', 'Vendor']}
                    rows={tableRows}
                  />
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text as="h3" variant="headingMd">
                    Export for Facebook Ads
                  </Text>

                  <InlineStack gap="400">
                    <div style={{ minWidth: '200px' }}>
                      <Select
                        label="Export Format"
                        options={[
                          { label: 'Facebook Catalog Format', value: 'facebook' },
                          { label: 'Product IDs Only', value: 'ids-only' },
                          { label: 'Product URLs', value: 'urls' }
                        ]}
                        value={exportFormat}
                        onChange={setExportFormat}
                      />
                    </div>
                    
                    <Button
                      variant="primary"
                      onClick={generateExportData}
                      disabled={selectedProducts.length === 0}
                    >
                      Generate Export ({selectedProducts.length} products)
                    </Button>
                  </InlineStack>

                  {showExported && (
                    <BlockStack gap="300">
                      <InlineStack gap="200">
                        <Button onClick={downloadData}>
                          Download File
                        </Button>
                        <Button onClick={copyToClipboard}>
                          Copy to Clipboard
                        </Button>
                      </InlineStack>
                      
                      <TextField
                        label="Exported Data"
                        value={exportedData}
                        multiline={10}
                        readOnly
                        autoComplete="off"
                        helpText={
                          exportFormat === 'facebook' 
                            ? "Tab-separated format ready for Facebook Product Catalog import"
                            : exportFormat === 'ids-only'
                            ? "Product IDs (one per line) for Facebook Custom Audiences"
                            : "Product URLs for Facebook Dynamic Ads"
                        }
                      />
                    </BlockStack>
                  )}
                </BlockStack>
              </Card>
            </Layout.Section>
          </>
        )}
      </Layout>
    </Page>
  );
} 