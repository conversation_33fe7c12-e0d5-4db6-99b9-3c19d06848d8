import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  AppProvider as PolarisAppProvider,
  Card,
  Page,
  Text,
  Banner,
  BlockStack,
} from "@shopify/polaris";
import polarisTranslations from "@shopify/polaris/locales/en.json";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // Check if this is a proper Shopify installation request
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  
  // If no shop parameter, this is not a valid Shopify installation flow
  if (!shop) {
    return { 
      isValidRequest: false,
      polarisTranslations 
    };
  }

  // If we have a shop parameter, redirect to proper Shopify auth
  throw redirect(`/auth/shopify?shop=${shop}`);
};

export const action = async ({ request }: ActionFunctionArgs) => {
  // No manual authentication allowed
  throw redirect("/");
};

export default function Auth() {
  const { isValidRequest, polarisTranslations } = useLoaderData<typeof loader>();

  return (
    <PolarisAppProvider i18n={polarisTranslations}>
      <Page>
        <Card>
          <BlockStack gap="400">
            <Text variant="headingMd" as="h2">
              Installation Required
            </Text>
            
            <Banner tone="critical">
              <Text as="p">
                This app must be installed through the Shopify App Store. 
                Manual domain entry is not permitted.
              </Text>
            </Banner>

            <Text as="p">
              To install this app:
            </Text>
            
            <Text as="p">
              1. Visit the Shopify App Store<br/>
              2. Search for "Direct Checkout Links"<br/>
              3. Click "Install" on your store<br/>
              4. Follow the installation prompts
            </Text>

            <Text as="p" tone="subdued">
              Apps must be installed and initiated only through Shopify services for security and compliance.
            </Text>
          </BlockStack>
        </Card>
      </Page>
    </PolarisAppProvider>
  );
}
