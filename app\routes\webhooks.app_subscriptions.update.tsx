import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import crypto from "crypto";
import prisma from "../db.server";

function verifyWebhook(body: string, signature: string): boolean {
  const secret = process.env.SHOPIFY_WEBHOOK_SECRET;
  if (!secret) {
    console.error("SHOPIFY_WEBHOOK_SECRET not set");
    return false;
  }

  const computedSignature = crypto
    .createHmac("sha256", secret)
    .update(body, "utf8")
    .digest("base64");

  return computedSignature === signature;
}

export const action = async ({ request }: ActionFunctionArgs) => {
  const signature = request.headers.get("x-shopify-hmac-sha256");
  const body = await request.text();

  if (!signature || !verifyWebhook(body, signature)) {
    console.error("Invalid webhook signature for app_subscriptions/update");
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const data = JSON.parse(body);
    
    console.log("Received app_subscriptions/update webhook:", data);

    const {
      id: subscriptionId,
      status,
      app_installation: { shop_domain },
      billing_attempts,
      current_period_end
    } = data;

    if (!shop_domain || !subscriptionId) {
      console.error("Missing required fields in subscription webhook");
      return json({ error: "Invalid webhook data" }, { status: 400 });
    }

    // Map Shopify subscription status to our internal status
    let internalStatus: string;
    switch (status) {
      case 'ACTIVE':
        internalStatus = 'active';
        break;
      case 'CANCELLED':
      case 'EXPIRED':
        internalStatus = 'cancelled';
        break;
      case 'PENDING':
        internalStatus = 'pending';
        break;
      case 'DECLINED':
        internalStatus = 'expired';
        break;
      default:
        internalStatus = 'expired';
    }

    // Update subscription in database
    await (prisma as any).subscription.upsert({
      where: { shop: shop_domain },
      update: {
        subscriptionId,
        status: internalStatus,
        currentPeriodEnd: current_period_end ? new Date(current_period_end) : null,
        updatedAt: new Date()
      },
      create: {
        shop: shop_domain,
        subscriptionId,
        status: internalStatus,
        currentPeriodEnd: current_period_end ? new Date(current_period_end) : null,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log(`Updated subscription for shop ${shop_domain}: ${internalStatus}`);

    // Log billing attempts for monitoring
    if (billing_attempts && billing_attempts.length > 0) {
      console.log(`Shop ${shop_domain} has ${billing_attempts.length} billing attempts`);
      
      // If there are failed billing attempts, we might want to handle them
      const failedAttempts = billing_attempts.filter((attempt: any) => 
        attempt.status === 'failed' || attempt.status === 'declined'
      );
      
      if (failedAttempts.length > 0) {
        console.warn(`Shop ${shop_domain} has ${failedAttempts.length} failed billing attempts`);
      }
    }

    return json({ 
      status: "success",
      message: "Subscription updated successfully",
      shop: shop_domain,
      subscription_status: internalStatus
    });

  } catch (error) {
    console.error("Error processing app_subscriptions/update webhook:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
}; 