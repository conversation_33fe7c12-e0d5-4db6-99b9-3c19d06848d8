# Deploy Direct Checkout Links App on EasyPanel.io

This guide will help you deploy the Shopify Direct Checkout Links app on EasyPanel.io.

## Prerequisites

1. **EasyPanel.io account** with a server
2. **Shopify Partner account** with app credentials
3. **Shopify tunnel URL** (ngrok, cloudflare tunnel, or public domain)

## Deployment Steps

### 1. Create PostgreSQL Database

In EasyPanel, create a new PostgreSQL service:
- **Service Name**: `direct-checkout-db`
- **Database Name**: `direct_checkout_links`
- **Username**: `shopify_app`
- **Password**: (generate a secure password)

### 2. Create App Service

Create a new app service in EasyPanel:
- **Service Name**: `direct-checkout-links`
- **Source**: GitHub repository
- **Build Type**: Dockerfile
- **Port**: 3000

### 3. Environment Variables

Set these environment variables in your EasyPanel app:

```env
# Database Connection
DATABASE_URL=*****************************************************************/direct_checkout_links

# Shopify App Credentials
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret

# Scopes (required)
SCOPES=read_products,unauthenticated_read_product_listings,unauthenticated_write_checkouts

# App URL (your EasyPanel domain or custom domain)
SHOPIFY_APP_URL=https://your-app-domain.easypanel.host
HOST=https://your-app-domain.easypanel.host

# Node Environment
NODE_ENV=production
```

### 4. Shopify App Configuration

In your Shopify Partner Dashboard:

1. **App URL**: `https://your-app-domain.easypanel.host`
2. **Allowed redirection URLs**: 
   - `https://your-app-domain.easypanel.host/auth/callback`
   - `https://your-app-domain.easypanel.host/auth/shopify/callback`

### 5. Deploy

1. **Push your code** to GitHub repository
2. **Deploy** the app in EasyPanel
3. **Database will auto-migrate** on first startup via the `docker-start` script

## File Structure Check

Ensure these files are ready for deployment:

✅ **Dockerfile** - Container configuration  
✅ **prisma/schema.prisma** - Database schema  
✅ **package.json** - Dependencies and scripts  
✅ **app/shopify.server.ts** - Shopify configuration  
✅ **app/db.server.ts** - Database connection  

## Features Ready for Production

- ✅ **PostgreSQL Database** - Persistent data storage
- ✅ **Session Management** - Database-backed sessions
- ✅ **Auto-checkout Settings** - Stored in database
- ✅ **Link Generation** - Four types of checkout links
- ✅ **Theme Integration** - JavaScript for product pages
- ✅ **Analytics Ready** - Link click tracking capability

## Post-Deployment Setup

### 1. Install App
- Visit your app URL in a browser
- Complete Shopify OAuth flow
- Install the app in your test store

### 2. Configure Theme
- Go to Theme Setup page in the app
- Follow instructions to add the JavaScript code to your theme
- Test auto-checkout functionality

### 3. Enable Auto-checkout
- Go to Settings page in the app
- Toggle the global auto-checkout setting
- Test with `?direct-checkout=true` parameter

## Testing

Test these URLs after deployment:
- `https://your-app-domain.easypanel.host` - App home page
- `https://your-app-domain.easypanel.host/auth/shopify` - Shopify auth
- `https://your-store.myshopify.com/products/any-product?direct-checkout=true` - Auto-checkout

## Troubleshooting

### Database Connection Issues
- Check DATABASE_URL format
- Ensure database service is running
- Verify network connectivity between services

### App Installation Issues  
- Verify SHOPIFY_API_KEY and SHOPIFY_API_SECRET
- Check redirect URLs in Shopify Partner Dashboard
- Ensure SHOPIFY_APP_URL matches your domain

### Auto-checkout Not Working
- Check if theme code is installed
- Verify global setting is enabled
- Test product page with browser dev tools

## Scaling Considerations

- **Database**: Consider connection pooling for high traffic
- **Analytics**: The link_analytics table will grow over time
- **Caching**: Consider Redis for session caching if needed

---

Your Direct Checkout Links app is now ready for production on EasyPanel.io! 🚀 