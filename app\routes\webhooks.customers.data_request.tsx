import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import crypto from "crypto";

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Verify webhook authenticity
    const body = await request.text();
    const hmacHeader = request.headers.get("X-Shopify-Hmac-Sha256");
    
    if (!hmacHeader) {
      console.error("Missing HMAC header for customers/data_request webhook");
      return json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify HMAC
    const hash = crypto
      .createHmac("sha256", process.env.SHOPIFY_WEBHOOK_SECRET || "")
      .update(body, "utf8")
      .digest("base64");

    if (hash !== hmacHeader) {
      console.error("Invalid HMAC for customers/data_request webhook");
      return json({ error: "Unauthorized" }, { status: 401 });
    }

    const payload = JSON.parse(body);
    console.log("Received customers/data_request webhook:", payload);

    const { shop_id, shop_domain, customer, orders_requested } = payload;
    
    // Log the data request for compliance tracking
    console.log("Customer data request for:", {
      shop: shop_domain,
      customer_id: customer?.id,
      customer_email: customer?.email,
      timestamp: new Date().toISOString()
    });

    // Our app doesn't store personal customer data
    // We only store shop-level data and product information
    const response = {
      status: "processed",
      message: "Direct Checkout Links app does not store personal customer data",
      data_collected: {
        personal_information: "None",
        explanation: "App only generates product-based checkout links without storing customer data",
        data_location: "No customer data stored in our systems"
      }
    };

    console.log("Data request response:", response);

    return json(response, { status: 200 });

  } catch (error) {
    console.error("Error processing customers/data_request webhook:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};