# Environment Variables

This document lists all environment variables required and optional for the Direct Checkout Links Shopify app.

## Required Variables

### Database Configuration

**`DATABASE_URL`** *(Required)*
- **Description**: PostgreSQL database connection string
- **Format**: `postgresql://username:password@host:port/database`
- **Example**: `postgresql://shopify_app:mypassword@localhost:5432/direct_checkout_links`
- **Production**: `*********************************************************/direct_checkout_links`

### Shopify App Credentials

**`SHOPIFY_API_KEY`** *(Required)*
- **Description**: Your Shopify app's API key from Partner Dashboard
- **Example**: `1234567890abcdef1234567890abcdef`
- **Where to find**: Shopify Partner Dashboard > Your App > App Setup

**`SHOPIFY_API_SECRET`** *(Required)*
- **Description**: Your Shopify app's API secret from Partner Dashboard
- **Example**: `abcdef1234567890abcdef1234567890ab`
- **Where to find**: Shopify Partner Dashboard > Your App > App Setup

### App URLs

**`SHOPIFY_APP_URL`** *(Required)*
- **Description**: The public URL where your app is hosted
- **Development**: `https://your-tunnel.ngrok.io`
- **Production**: `https://your-app-domain.easypanel.host`
- **Note**: Must match the App URL in Shopify Partner Dashboard

**`HOST`** *(Required)*
- **Description**: The host URL for the app (usually same as SHOPIFY_APP_URL)
- **Development**: `https://your-tunnel.ngrok.io`
- **Production**: `https://your-app-domain.easypanel.host`

### App Permissions

**`SCOPES`** *(Required)*
- **Description**: Comma-separated list of Shopify API scopes
- **Value**: `read_products,unauthenticated_read_product_listings,unauthenticated_write_checkouts`
- **Note**: These are the minimum required scopes for the app to function

## Optional Variables

### Environment

**`NODE_ENV`** *(Optional)*
- **Description**: Node.js environment mode
- **Values**: `development` | `production` | `test`
- **Default**: `development`
- **Production**: `production`

### Custom Domains

**`SHOP_CUSTOM_DOMAIN`** *(Optional)*
- **Description**: Custom shop domain if using Shopify Plus custom domains
- **Example**: `shop.mycustomdomain.com`
- **Note**: Only needed if your shop uses a custom domain

## Environment File Examples

### Development (.env)
```env
# Database
DATABASE_URL="postgresql://shopify_app:password@localhost:5432/direct_checkout_links"

# Shopify App
SHOPIFY_API_KEY="your_api_key_here"
SHOPIFY_API_SECRET="your_api_secret_here"
SCOPES="read_products,unauthenticated_read_product_listings,unauthenticated_write_checkouts"

# URLs (using ngrok tunnel)
SHOPIFY_APP_URL="https://abc123.ngrok.io"
HOST="https://abc123.ngrok.io"

# Environment
NODE_ENV="development"
```

### Production (EasyPanel.io)
```env
# Database (internal container networking)
DATABASE_URL="*********************************************************************/direct_checkout_links"

# Shopify App
SHOPIFY_API_KEY="your_production_api_key"
SHOPIFY_API_SECRET="your_production_api_secret"
SCOPES="read_products,unauthenticated_read_product_listings,unauthenticated_write_checkouts"

# URLs (your production domain)
SHOPIFY_APP_URL="https://direct-checkout-links.easypanel.host"
HOST="https://direct-checkout-links.easypanel.host"

# Environment
NODE_ENV="production"
```

## Security Notes

⚠️ **Important Security Guidelines:**

1. **Never commit .env files** to version control
2. **Use strong database passwords** (16+ characters, mixed case, numbers, symbols)
3. **Keep API secrets secure** - never expose in client-side code
4. **Use HTTPS only** in production
5. **Rotate secrets regularly** especially API keys

## Validation

The app will validate these environment variables on startup:

- ✅ **DATABASE_URL** - Tests database connection
- ✅ **SHOPIFY_API_KEY** - Required for Shopify authentication
- ✅ **SHOPIFY_API_SECRET** - Required for Shopify authentication
- ✅ **SCOPES** - Must include minimum required scopes
- ✅ **SHOPIFY_APP_URL** - Must be a valid HTTPS URL

## Troubleshooting

### Common Issues

**Database connection failed**
- Check DATABASE_URL format
- Verify database is running and accessible
- Test connection manually: `psql $DATABASE_URL`

**Shopify authentication failed**
- Verify SHOPIFY_API_KEY and SHOPIFY_API_SECRET
- Check that URLs match in Partner Dashboard
- Ensure SCOPES include required permissions

**App not loading**
- Verify SHOPIFY_APP_URL is publicly accessible
- Check that HOST matches SHOPIFY_APP_URL
- Test URL in browser: `curl https://your-app-url/health`

## App Billing Information

**Note about billing:** App subscriptions are handled through Shopify's Partner API and don't require special scopes in your app. Billing functionality will be implemented using:

- **Admin API**: For app functionality and data access
- **Partner API**: For billing and subscription management (handled server-side)
- **App Bridge**: For billing confirmation flows

## Health Check

Test your environment setup:
```bash
# Test database connection
curl https://your-app-url/health

# Expected response:
{
  "status": "healthy",
  "database": "connected",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "1.0.0"
}
``` 