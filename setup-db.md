# PostgreSQL Database Setup for Direct Checkout Links App

## Prerequisites

1. **Install PostgreSQL** on your system
2. **Create a database** for the app

## Setup Steps

### 1. Create Database
```sql
-- Connect to PostgreSQL as superuser
CREATE DATABASE direct_checkout_links;
CREATE USER shopify_app WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE direct_checkout_links TO shopify_app;
```

### 2. Set Environment Variables
Create a `.env` file in your project root with:

```env
DATABASE_URL="postgresql://shopify_app:your_secure_password@localhost:5432/direct_checkout_links"
SHOPIFY_API_KEY="your_api_key"
SHOPIFY_API_SECRET="your_api_secret"
SCOPES="read_products,unauthenticated_read_product_listings,unauthenticated_write_checkouts"
HOST="your_tunnel_url"
SHOPIFY_APP_URL="your_tunnel_url"
```

### 3. Generate Prisma Client
```bash
npx prisma generate
```

### 4. Run Database Migrations
```bash
npx prisma migrate dev --name init
```

### 5. Alternative: Manual Table Creation
If Prisma commands fail, create tables manually:

```sql
-- Connect to your database
\c direct_checkout_links;

-- Create Session table
CREATE TABLE "Session" (
  "id" TEXT PRIMARY KEY,
  "shop" TEXT NOT NULL,
  "state" TEXT NOT NULL,
  "isOnline" BOOLEAN DEFAULT false,
  "scope" TEXT,
  "expires" TIMESTAMP,
  "accessToken" TEXT NOT NULL,
  "userId" BIGINT,
  "firstName" TEXT,
  "lastName" TEXT,
  "email" TEXT,
  "accountOwner" BOOLEAN DEFAULT false,
  "locale" TEXT,
  "collaborator" BOOLEAN DEFAULT false,
  "emailVerified" BOOLEAN DEFAULT false
);

-- Create auto_checkout_settings table
CREATE TABLE "auto_checkout_settings" (
  "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "shop" TEXT UNIQUE NOT NULL,
  "enabled" BOOLEAN DEFAULT false,
  "createdAt" TIMESTAMP DEFAULT NOW(),
  "updatedAt" TIMESTAMP DEFAULT NOW()
);

-- Create generated_links table
CREATE TABLE "generated_links" (
  "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "shop" TEXT NOT NULL,
  "productId" TEXT NOT NULL,
  "variantId" TEXT NOT NULL,
  "quantity" INTEGER DEFAULT 1,
  "checkoutUrl" TEXT NOT NULL,
  "cartUrl" TEXT NOT NULL,
  "directProductUrl" TEXT,
  "advancedDirectUrl" TEXT,
  "productTitle" TEXT,
  "variantTitle" TEXT,
  "price" TEXT,
  "createdAt" TIMESTAMP DEFAULT NOW()
);

-- Create indexes
CREATE INDEX "generated_links_shop_createdAt_idx" ON "generated_links"("shop", "createdAt");
CREATE INDEX "generated_links_shop_productId_idx" ON "generated_links"("shop", "productId");

-- Create link_analytics table
CREATE TABLE "link_analytics" (
  "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  "shop" TEXT NOT NULL,
  "linkId" TEXT,
  "linkType" TEXT NOT NULL,
  "productId" TEXT,
  "variantId" TEXT,
  "clicked" TIMESTAMP DEFAULT NOW(),
  "userAgent" TEXT,
  "referer" TEXT,
  "ipAddress" TEXT
);

-- Create indexes for analytics
CREATE INDEX "link_analytics_shop_clicked_idx" ON "link_analytics"("shop", "clicked");
CREATE INDEX "link_analytics_linkType_clicked_idx" ON "link_analytics"("linkType", "clicked");

-- Grant permissions to app user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO shopify_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO shopify_app;
```

## Start the App

```bash
npm run dev
```

## Benefits of PostgreSQL Setup

- ✅ **Persistent Sessions** - Sessions survive app restarts
- ✅ **Auto-checkout Settings** - Stored in database instead of metafields
- ✅ **Link History** - All generated links saved for analytics
- ✅ **Better Performance** - Database queries faster than API calls
- ✅ **Analytics Ready** - Track link usage and clicks 