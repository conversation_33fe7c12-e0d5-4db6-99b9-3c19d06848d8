import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import crypto from "crypto";

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Verify webhook authenticity
    const body = await request.text();
    const hmacHeader = request.headers.get("X-Shopify-Hmac-Sha256");
    
    if (!hmacHeader) {
      console.error("Missing HMAC header for customers/redact webhook");
      return json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify HMAC
    const hash = crypto
      .createHmac("sha256", process.env.SHOPIFY_WEBHOOK_SECRET || "")
      .update(body, "utf8")
      .digest("base64");

    if (hash !== hmacHeader) {
      console.error("Invalid HMAC for customers/redact webhook");
      return json({ error: "Unauthorized" }, { status: 401 });
    }

    const payload = JSON.parse(body);
    console.log("Received customers/redact webhook:", payload);

    const { shop_id, shop_domain, customer, orders_to_redact } = payload;
    
    // Log the redaction request
    console.log("Customer redaction request for:", {
      shop: shop_domain,
      customer_id: customer?.id,
      customer_email: customer?.email,
      orders_to_redact: orders_to_redact || [],
      timestamp: new Date().toISOString()
    });

    // Since our app doesn't store personal customer data, there's nothing to redact
    // We only store:
    // - Shop-level settings
    // - Product information 
    // - Generated links (not tied to customer identity)
    
    const response = {
      status: "completed",
      message: "Customer data redaction completed",
      actions_taken: {
        customer_data: "No personal customer data was stored to redact",
        explanation: "App only stores shop-level and product data",
        compliance: "Fully compliant - no customer data retention"
      }
    };

    console.log("Redaction completed:", response);

    return json(response, { status: 200 });

  } catch (error) {
    console.error("Error processing customers/redact webhook:", error);
    return json({ error: "Internal server error" }, { status: 500 });
  }
};