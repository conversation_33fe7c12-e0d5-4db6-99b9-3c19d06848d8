import {
  Page,
  Layout,
  Text,
  Card,
  BlockStack,
  Box,
  Button,
  Frame,
  Toast,
  Banner,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useState } from "react";

export default function ThemeSetup() {
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setToastMessage(`${type} copied to clipboard!`);
      setShowToast(true);
    } catch (err) {
      setToastMessage("Failed to copy to clipboard");
      setShowToast(true);
    }
  };

  const themeCode = `<script>
(function() { 
    const urlParams = new URLSearchParams(window.location.search);
    const directCheckout = urlParams.get('direct-checkout');
    if (directCheckout === 'true') {
        console.log('[DirectCheckout] Processing auto-checkout...');
        processAutoCheckout();
    }
    function processAutoCheckout() {
        const urlParams = new URLSearchParams(window.location.search);
        let variantId = urlParams.get('variant');
        let quantity = urlParams.get('quantity') || '1';
        if (!variantId) {
            const variantSelect = document.querySelector('[name="id"], [name="variant"], select[name*="variant"]');
            const variantInput = document.querySelector('input[name="id"], input[name="variant"]');
            if (variantSelect && variantSelect.value) {
                variantId = variantSelect.value;
                console.log('[DirectCheckout] Using form select variant:', variantId);
            } else if (variantInput && variantInput.value) {
                variantId = variantInput.value;
                console.log('[DirectCheckout] Using form input variant:', variantId);
            }
            else {
                const productForm = document.querySelector('[action*="/cart/add"]');
                if (productForm) {
                    const hiddenVariant = productForm.querySelector('input[name="id"]');
                    if (hiddenVariant) {
                        variantId = hiddenVariant.value;
                        console.log('[DirectCheckout] Using hidden form variant:', variantId);
                    }
                }
            }
        }
        if (!variantId) {
            console.error('[DirectCheckout] Could not determine product variant for auto-checkout');
            return;
        }
        console.log('[DirectCheckout] Adding to cart - Variant:', variantId, 'Quantity:', quantity);
        const formData = new FormData();
        formData.append('id', variantId);
        formData.append('quantity', quantity);
        fetch('/cart/add.js', { method: 'POST', body: formData })
            .then(response => {
                console.log('[DirectCheckout] Cart add response:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('[DirectCheckout] Cart add success:', data);
                window.location.href = '/checkout';
            })
            .catch(error => {
                console.error('[DirectCheckout] Error adding to cart:', error);
            });
    }
})();
</script>`;

  const liquidCode = `{% unless request.design_mode %}
<script>
(function() { 
    const urlParams = new URLSearchParams(window.location.search);
    const directCheckout = urlParams.get('direct-checkout');
    if (directCheckout === 'true') {
        console.log('[DirectCheckout] Processing auto-checkout...');
        processAutoCheckout();
    }
    function processAutoCheckout() {
        const urlParams = new URLSearchParams(window.location.search);
        let variantId = urlParams.get('variant');
        let quantity = urlParams.get('quantity') || '1';
        if (!variantId) {
            if (typeof window.product !== 'undefined' && window.product.variants && window.product.variants.length > 0) {
                variantId = window.product.variants[0].id;
                console.log('[DirectCheckout] Using product object variant:', variantId);
            }
            else {
                const variantSelect = document.querySelector('[name="id"], [name="variant"], select[name*="variant"]');
                const variantInput = document.querySelector('input[name="id"], input[name="variant"]');
                if (variantSelect && variantSelect.value) {
                    variantId = variantSelect.value;
                    console.log('[DirectCheckout] Using form select variant:', variantId);
                } else if (variantInput && variantInput.value) {
                    variantId = variantInput.value;
                    console.log('[DirectCheckout] Using form input variant:', variantId);
                }
            }
        }
        if (!variantId) {
            console.error('[DirectCheckout] Could not determine product variant for auto-checkout');
            return;
        }
        console.log('[DirectCheckout] Adding to cart - Variant:', variantId, 'Quantity:', quantity);
        document.body.style.opacity = '0.5';
        const formData = new FormData();
        formData.append('id', variantId);
        formData.append('quantity', quantity);
        fetch(window.Shopify.routes.root + 'cart/add.js', { method: 'POST', body: formData })
            .then(response => {
                console.log('[DirectCheckout] Cart add response:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('[DirectCheckout] Cart add success:', data);
                window.location.href = window.Shopify.routes.root + 'checkout';
            })
            .catch(error => {
                console.error('[DirectCheckout] Error adding to cart:', error);
                document.body.style.opacity = '1';
            });
    }
})();
</script>
<script>
window.product = {{ product | json }};
</script>
{% endunless %}`;

  return (
    <Frame>
      <Page>
        <TitleBar title="Theme Setup Instructions" />
        <BlockStack gap="500">
          <Layout>
            <Layout.Section>
              <Banner title="Theme Setup Required" tone="info">
                <p>
                  To enable auto-checkout product links, you need to add JavaScript code to your theme's product page template.
                  This code will detect the direct-checkout parameter and automatically redirect customers to checkout.
                </p>
              </Banner>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="500">
                  <Text as="h2" variant="headingMd">
                    Installation Instructions
                  </Text>
                  
                  <BlockStack gap="400">
                    <Text variant="bodyMd" as="p">
                      <strong>Step 1:</strong> Copy the code below
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      <strong>Step 2:</strong> In your Shopify admin, go to Online Store → Themes → Edit code
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      <strong>Step 3:</strong> Find your product.liquid template (usually in templates/product.liquid)
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      <strong>Step 4:</strong> Add the code before the closing {'</body>'} tag or at the end of the template
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      <strong>Step 5:</strong> Save the file
                    </Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Box paddingBlockEnd="200">
                    <Text as="h3" variant="headingSm">
                      Liquid Template Code (Recommended)
                    </Text>
                  </Box>
                  
                  <Box padding="400" background="bg-surface-secondary" borderRadius="200">
                    <Text as="p" variant="bodySm">
                      {liquidCode}
                    </Text>
                  </Box>
                  
                  <Box paddingBlockStart="200">
                    <Button
                      onClick={() => copyToClipboard(liquidCode, "Liquid template code")}
                    >
                      Copy Liquid Code
                    </Button>
                  </Box>
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Box paddingBlockEnd="200">
                    <Text as="h3" variant="headingSm">
                      Alternative: Plain JavaScript Code
                    </Text>
                  </Box>
                  
                  <Box padding="400" background="bg-surface-secondary" borderRadius="200">
                    <Text as="p" variant="bodySm">
                      {themeCode}
                    </Text>
                  </Box>
                  
                  <Box paddingBlockStart="200">
                    <Button
                      onClick={() => copyToClipboard(themeCode, "JavaScript code")}
                    >
                      Copy JavaScript Code
                    </Button>
                  </Box>
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text as="h3" variant="headingSm">
                    How It Works
                  </Text>
                  
                  <BlockStack gap="300">
                    <Text variant="bodyMd" as="p">
                      1. <strong>Parameter Detection:</strong> It checks for the `direct-checkout=true` parameter in the URL
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      2. <strong>Variant Detection:</strong> It automatically detects the product's default variant or uses the one specified in URL
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      3. <strong>Add to Cart:</strong> The product is automatically added to the cart with quantity 1 (or custom quantity)
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      4. <strong>Redirect:</strong> Customer is immediately redirected to checkout
                    </Text>
                    
                    <Text variant="bodyMd" as="p">
                      5. <strong>Fallback:</strong> If there's an error, the normal product page is shown
                    </Text>
                  </BlockStack>
                </BlockStack>
              </Card>
            </Layout.Section>

            <Layout.Section>
              <Card>
                <BlockStack gap="400">
                  <Text as="h3" variant="headingSm">
                    Example Auto-Checkout URLs
                  </Text>
                  
                  <BlockStack gap="300">
                    <Box>
                      <Text variant="bodyMd" fontWeight="semibold" as="p">
                        Simple Format (Recommended):
                      </Text>
                      <Box padding="300" background="bg-surface-secondary" borderRadius="200">
                        <Text variant="bodySm" as="p">
                          https://yourstore.myshopify.com/products/t-shirt?direct-checkout=true
                        </Text>
                      </Box>
                      <Text variant="bodySm" tone="subdued" as="p">
                        Automatically adds the default variant with quantity 1 to cart and redirects to checkout.
                      </Text>
                    </Box>
                    
                    <Box>
                      <Text variant="bodyMd" fontWeight="semibold" as="p">
                        With Custom Quantity:
                      </Text>
                      <Box padding="300" background="bg-surface-secondary" borderRadius="200">
                        <Text variant="bodySm" as="p">
                          https://yourstore.myshopify.com/products/t-shirt?direct-checkout=true&quantity=2
                        </Text>
                      </Box>
                      <Text variant="bodySm" tone="subdued" as="p">
                        Adds 2 units of the default variant to cart and redirects to checkout.
                      </Text>
                    </Box>
                    
                    <Box>
                      <Text variant="bodyMd" fontWeight="semibold" as="p">
                        With Specific Variant:
                      </Text>
                      <Box padding="300" background="bg-surface-secondary" borderRadius="200">
                        <Text variant="bodySm" as="p">
                          https://yourstore.myshopify.com/products/t-shirt?direct-checkout=true&variant=12345678&quantity=3
                        </Text>
                      </Box>
                      <Text variant="bodySm" tone="subdued" as="p">
                        Adds 3 units of a specific variant to cart and redirects to checkout.
                      </Text>
                    </Box>
                  </BlockStack>
                </BlockStack>
              </Card>
            </Layout.Section>
          </Layout>
        </BlockStack>
        
        {showToast && (
          <Toast
            content={toastMessage}
            onDismiss={() => setShowToast(false)}
          />
        )}
      </Page>
    </Frame>
  );
}