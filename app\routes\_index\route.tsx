import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";

import styles from "./styles.module.css";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);

  if (url.searchParams.get("shop")) {
    throw redirect(`/app?${url.searchParams.toString()}`);
  }

  return { showForm: false }; // Always false to prevent manual entry
};

export default function App() {
  const { showForm } = useLoaderData<typeof loader>();

  return (
    <div className={styles.index}>
      <div className={styles.content}>
        <h1 className={styles.heading}>Direct Checkout Links</h1>
        <p className={styles.text}>
          Generate direct checkout links for your Shopify products to streamline customer purchasing.
        </p>
        <p className={styles.text}>
          This app must be installed through the Shopify App Store.
        </p>
        <ul className={styles.list}>
          <li>
            <strong>Auto-checkout Links</strong>. Create links that automatically add products to cart and redirect to checkout.
          </li>
          <li>
            <strong>Multiple Link Types</strong>. Generate cart URLs, checkout URLs, and direct product links.
          </li>
          <li>
            <strong>Easy Integration</strong>. Simple theme setup with JavaScript code for seamless auto-checkout experience.
          </li>
        </ul>
      </div>
    </div>
  );
}
