import { useEffect, useState, useCallback } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useFetcher, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  Box,
  List,
  Link,
  InlineStack,
  Select,
  TextField,
  Badge,
  Frame,
  Toast,
  ResourceList,
  ResourceItem,
  Thumbnail,
  ButtonGroup,
} from "@shopify/polaris";
import { TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { authenticate, unauthenticated } from "../shopify.server";
import { saveGeneratedLink } from "../services/autoCheckout.server";

interface Product {
  id: string;
  title: string;
  handle: string;
  featuredImage?: {
    url: string;
    altText?: string;
  };
  variants: {
    edges: Array<{
      node: {
        id: string;
        title: string;
        price: string;
        availableForSale: boolean;
      };
    }>;
  };
}

interface CheckoutLink {
  productId: string;
  variantId: string;
  quantity: number;
  checkoutUrl: string;
  cartUrl: string;
  directProductUrl: string;
  advancedDirectUrl: string;
}

interface ActionData {
  success: boolean;
  checkoutUrl?: string;
  cartUrl?: string;
  directProductUrl?: string;
  advancedDirectUrl?: string;
  variantId?: string;
  quantity?: number;
  error?: string;
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { admin } = await authenticate.admin(request);

  const response = await admin.graphql(
    `#graphql
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              featuredImage {
                url
                altText
              }
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    price
                    availableForSale
                  }
                }
              }
            }
          }
        }
      }`,
    {
      variables: {
        first: 50,
      },
    },
  );

  const responseJson = await response.json();
  const products = responseJson.data?.products?.edges?.map((edge: any) => edge.node) || [];

  return json({ products });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { admin, session } = await authenticate.admin(request);
  const formData = await request.formData();
  const actionType = formData.get("action");

  if (actionType === "generateLinks") {
    const variantId = formData.get("variantId") as string;
    const quantity = parseInt(formData.get("quantity") as string) || 1;

    try {
      // First, create a Storefront access token using Admin API
      const storefrontTokenResponse = await admin.graphql(
        `#graphql
          mutation storefrontAccessTokenCreate($input: StorefrontAccessTokenInput!) {
            storefrontAccessTokenCreate(input: $input) {
              storefrontAccessToken {
                accessToken
              }
              userErrors {
                field
                message
              }
            }
          }`,
        {
          variables: {
            input: {
              title: "Direct Checkout Links Generator Token",
            },
          },
        },
      );

      const tokenResult = await storefrontTokenResponse.json();
      
      if (tokenResult.data?.storefrontAccessTokenCreate?.userErrors?.length > 0) {
        throw new Error(tokenResult.data.storefrontAccessTokenCreate.userErrors[0].message);
      }

      const accessToken = tokenResult.data?.storefrontAccessTokenCreate?.storefrontAccessToken?.accessToken;
      
      if (!accessToken) {
        throw new Error("Failed to create Storefront access token");
      }

      // Now use the access token to create a cart with the Storefront API
      const storefrontResponse = await fetch(`https://${session.shop}/api/2025-04/graphql.json`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Storefront-Access-Token': accessToken,
        },
        body: JSON.stringify({
          query: `
            mutation cartCreate($input: CartInput!) {
              cartCreate(input: $input) {
                cart {
                  id
                  checkoutUrl
                  lines(first: 10) {
                    edges {
                      node {
                        id
                        quantity
                        merchandise {
                          ... on ProductVariant {
                            id
                            title
                            product {
                              title
                            }
                          }
                        }
                      }
                    }
                  }
                }
                userErrors {
                  field
                  message
                  code
                }
              }
            }
          `,
          variables: {
            input: {
              lines: [
                {
                  merchandiseId: variantId,
                  quantity: quantity,
                },
              ],
            },
          },
        }),
      });

      const cartResponse = await storefrontResponse.json();
      
      if (cartResponse.errors) {
        throw new Error(cartResponse.errors[0]?.message || "GraphQL error occurred");
      }
      
      if (cartResponse.data?.cartCreate?.userErrors?.length > 0) {
        throw new Error(cartResponse.data.cartCreate.userErrors[0].message);
      }

      const cart = cartResponse.data?.cartCreate?.cart;
      const checkoutUrl = cart?.checkoutUrl;
      
      if (!checkoutUrl) {
        throw new Error("Failed to generate checkout URL");
      }

      // Clean up the temporary Storefront access token
      try {
        await admin.graphql(
          `#graphql
            mutation storefrontAccessTokenDelete($input: StorefrontAccessTokenDeleteInput!) {
              storefrontAccessTokenDelete(input: $input) {
                deletedStorefrontAccessTokenId
                userErrors {
                  field
                  message
                }
              }
            }`,
          {
            variables: {
              input: {
                storefrontAccessToken: accessToken,
              },
            },
          },
        );
      } catch (cleanupError) {
        // Log but don't fail the request if cleanup fails
        console.warn("Failed to cleanup Storefront access token:", cleanupError);
      }

      // Also generate cart link as alternative
      const variantIdNumber = variantId.replace('gid://shopify/ProductVariant/', '');
      const cartUrl = `https://${session.shop}/cart/${variantIdNumber}:${quantity}`;

      // Generate direct product URL with auto-checkout parameter
      // First, get the product handle from the variant
      const productResponse = await admin.graphql(
        `#graphql
          query getProductByVariant($variantId: ID!) {
            productVariant(id: $variantId) {
              product {
                handle
              }
            }
          }`,
        {
          variables: {
            variantId: variantId,
          },
        },
      );

      const productData = await productResponse.json();
      const productHandle = productData.data?.productVariant?.product?.handle;
      
      // Generate direct product URL with auto-checkout parameter (simplified format)
      const directProductUrl = productHandle 
        ? `https://${session.shop}/products/${productHandle}?direct-checkout=true`
        : '';
      
      // Also generate advanced format with specific variant and quantity
      const advancedDirectUrl = productHandle 
        ? `https://${session.shop}/products/${productHandle}?direct-checkout=true&variant=${variantIdNumber}&quantity=${quantity}`
        : '';

      // Get product and variant details for saving to database
      const productDetailsResponse = await admin.graphql(
        `#graphql
          query getProductDetails($variantId: ID!) {
            productVariant(id: $variantId) {
              id
              title
              price
              product {
                id
                title
                handle
              }
            }
          }`,
        {
          variables: {
            variantId: variantId,
          },
        },
      );

      const productDetails = await productDetailsResponse.json();
      const variant = productDetails.data?.productVariant;
      const product = variant?.product;

      // Save generated link to database for history and analytics
      if (product && variant) {
        await saveGeneratedLink({
          shop: session.shop,
          productId: product.id,
          variantId: variant.id,
          quantity: quantity,
          checkoutUrl,
          cartUrl,
          directProductUrl,
          advancedDirectUrl,
          productTitle: product.title,
          variantTitle: variant.title,
          price: variant.price,
        });
      }

      return json({
        success: true,
        checkoutUrl,
        cartUrl,
        directProductUrl,
        advancedDirectUrl,
        variantId,
        quantity,
      } as ActionData);
    } catch (error) {
      return json({
        success: false,
        error: error instanceof Error ? error.message : "Failed to generate checkout links",
      } as ActionData);
    }
  }

  return json({ success: false, error: "Invalid action" } as ActionData);
};

export default function Index() {
  const { products } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<ActionData>();
  const shopify = useAppBridge();

  const [selectedProduct, setSelectedProduct] = useState<string>("");
  const [selectedVariant, setSelectedVariant] = useState<string>("");
  const [quantity, setQuantity] = useState("1");
  const [generatedLinks, setGeneratedLinks] = useState<CheckoutLink[]>([]);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const selectedProductData = products.find((p: Product) => p.id === selectedProduct);
  const variants = selectedProductData?.variants?.edges || [];

  const productOptions = [
    { label: "Select a product", value: "" },
    ...products.map((product: Product) => ({
      label: product.title,
      value: product.id,
    })),
  ];

  const variantOptions = [
    { label: "Select a variant", value: "" },
    ...variants.map((variant: any) => ({
      label: `${variant.node.title} - $${variant.node.price}`,
      value: variant.node.id,
    })),
  ];

  const handleProductChange = useCallback((value: string) => {
    setSelectedProduct(value);
    setSelectedVariant("");
  }, []);

  const handleVariantChange = useCallback((value: string) => {
    setSelectedVariant(value);
  }, []);

  const handleQuantityChange = useCallback((value: string) => {
    setQuantity(value);
  }, []);

  const generateLinks = () => {
    if (!selectedVariant || !quantity) {
      setToastMessage("Please select a product variant and quantity");
      setShowToast(true);
      return;
    }

    const formData = new FormData();
    formData.append("action", "generateLinks");
    formData.append("variantId", selectedVariant);
    formData.append("quantity", quantity);

    fetcher.submit(formData, { method: "POST" });
  };

  useEffect(() => {
    if (fetcher.data?.success && fetcher.data.checkoutUrl && fetcher.data.cartUrl && 
        selectedProduct && selectedVariant) {
      
      // Check if this exact link already exists
      const linkExists = generatedLinks.some(link => 
        link.productId === selectedProduct && 
        link.variantId === selectedVariant && 
        link.quantity === parseInt(quantity) &&
        link.checkoutUrl === fetcher.data?.checkoutUrl
      );
      
      if (!linkExists) {
        const newLink: CheckoutLink = {
          productId: selectedProduct,
          variantId: selectedVariant,
          quantity: parseInt(quantity),
          checkoutUrl: fetcher.data.checkoutUrl,
          cartUrl: fetcher.data.cartUrl,
          directProductUrl: fetcher.data.directProductUrl || "",
          advancedDirectUrl: fetcher.data.advancedDirectUrl || "",
        };
        
        setGeneratedLinks(prev => [newLink, ...prev]);
        setToastMessage("Links generated successfully!");
        setShowToast(true);
        
        // Reset form
        setSelectedProduct("");
        setSelectedVariant("");
        setQuantity("1");
      }
    } else if (fetcher.data?.error) {
      setToastMessage(fetcher.data.error);
      setShowToast(true);
    }
  }, [fetcher.data?.success, fetcher.data?.checkoutUrl, fetcher.data?.error]);

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setToastMessage(`${type} copied to clipboard!`);
      setShowToast(true);
    } catch (err) {
      setToastMessage("Failed to copy to clipboard");
      setShowToast(true);
    }
  };

  const isLoading = ["loading", "submitting"].includes(fetcher.state);

  return (
    <Frame>
      <Page>
        <TitleBar title="Direct Checkout Links Generator" />
        <BlockStack gap="500">
          <Layout>
            <Layout.Section>
              <Card>
                <BlockStack gap="500">
                  <Text as="h2" variant="headingMd">
                    Create Direct Checkout Links
                  </Text>
                  <Text variant="bodyMd" as="p">
                    Generate multiple types of direct links for your products:
                  </Text>
                  <List type="bullet">
                    <List.Item><strong>Direct Checkout Link:</strong> Takes customers straight to checkout with items pre-loaded</List.Item>
                    <List.Item><strong>Simple Auto-Checkout Link:</strong> Just add <code>?direct-checkout=true</code> to any product URL</List.Item>
                    <List.Item><strong>Advanced Auto-Checkout Link:</strong> Specify exact variant and quantity with auto-redirect</List.Item>
                    <List.Item><strong>Cart Link:</strong> Adds items to cart for manual checkout</List.Item>
                  </List>
                  
                  <BlockStack gap="400">
                    <Select
                      label="Select Product"
                      options={productOptions}
                      value={selectedProduct}
                      onChange={handleProductChange}
                    />
                    
                    {selectedProduct && (
                      <Select
                        label="Select Variant"
                        options={variantOptions}
                        value={selectedVariant}
                        onChange={handleVariantChange}
                      />
                    )}
                    
                    {selectedVariant && (
                      <TextField
                        label="Quantity"
                        type="number"
                        value={quantity}
                        onChange={handleQuantityChange}
                        min="1"
                        autoComplete="off"
                      />
                    )}
                    
                    <InlineStack gap="300">
                      <Button
                        variant="primary"
                        loading={isLoading}
                        disabled={!selectedVariant}
                        onClick={generateLinks}
                      >
                        Generate Links
                      </Button>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>
            </Layout.Section>
            
            {generatedLinks.length > 0 && (
              <Layout.Section>
                <Card>
                  <BlockStack gap="400">
                    <Text as="h2" variant="headingMd">
                      Generated Links
                    </Text>
                    
                    <ResourceList
                      resourceName={{ singular: "link", plural: "links" }}
                      items={generatedLinks}
                      renderItem={(item, index) => {
                        const product = products.find((p: Product) => p.id === item.productId);
                        const variant = product?.variants.edges.find(
                          (v: any) => v.node.id === item.variantId
                        );
                        
                        return (
                          <ResourceItem
                            id={index.toString()}
                            onClick={() => {}}
                            media={
                              <Thumbnail
                                source={product?.featuredImage?.url || ""}
                                alt={product?.featuredImage?.altText || product?.title}
                              />
                            }
                          >
                            <BlockStack gap="200">
                              <Text as="h3" variant="bodyMd" fontWeight="bold">
                                {product?.title} - {variant?.node.title}
                              </Text>
                              <Text as="p" variant="bodySm" tone="subdued">
                                Quantity: {item.quantity} | Price: ${variant?.node.price}
                              </Text>
                              
                              <BlockStack gap="200">
                                <Box>
                                  <InlineStack gap="200" align="space-between">
                                    <Text as="span" variant="bodySm" fontWeight="semibold">
                                      Direct Checkout Link:
                                    </Text>
                                    <Button
                                      size="micro"
                                      onClick={() => copyToClipboard(item.checkoutUrl, "Checkout link")}
                                    >
                                      Copy
                                    </Button>
                                  </InlineStack>
                                  <Box paddingBlockStart="100">
                                    <Text as="p" variant="bodySm" tone="subdued" breakWord>
                                      {item.checkoutUrl}
                                    </Text>
                                  </Box>
                                </Box>
                                
                                {item.directProductUrl && (
                                  <Box>
                                    <InlineStack gap="200" align="space-between">
                                      <Text as="span" variant="bodySm" fontWeight="semibold">
                                        Simple Auto-Checkout Link:
                                      </Text>
                                      <Button
                                        size="micro"
                                        onClick={() => copyToClipboard(item.directProductUrl, "Simple auto-checkout link")}
                                      >
                                        Copy
                                      </Button>
                                    </InlineStack>
                                    <Box paddingBlockStart="100">
                                      <Text as="p" variant="bodySm" tone="subdued" breakWord>
                                        {item.directProductUrl}
                                      </Text>
                                    </Box>
                                  </Box>
                                )}
                                
                                {item.advancedDirectUrl && (
                                  <Box>
                                    <InlineStack gap="200" align="space-between">
                                      <Text as="span" variant="bodySm" fontWeight="semibold">
                                        Advanced Auto-Checkout Link:
                                      </Text>
                                      <Button
                                        size="micro"
                                        onClick={() => copyToClipboard(item.advancedDirectUrl, "Advanced auto-checkout link")}
                                      >
                                        Copy
                                      </Button>
                                    </InlineStack>
                                    <Box paddingBlockStart="100">
                                      <Text as="p" variant="bodySm" tone="subdued" breakWord>
                                        {item.advancedDirectUrl}
                                      </Text>
                                    </Box>
                                  </Box>
                                )}
                                
                                <Box>
                                  <InlineStack gap="200" align="space-between">
                                    <Text as="span" variant="bodySm" fontWeight="semibold">
                                      Cart Link:
                                    </Text>
                                    <Button
                                      size="micro"
                                      onClick={() => copyToClipboard(item.cartUrl, "Cart link")}
                                    >
                                      Copy
                                    </Button>
                                  </InlineStack>
                                  <Box paddingBlockStart="100">
                                    <Text as="p" variant="bodySm" tone="subdued" breakWord>
                                      {item.cartUrl}
                                    </Text>
                                  </Box>
                                </Box>
                              </BlockStack>
                            </BlockStack>
                          </ResourceItem>
                        );
                      }}
                    />
                  </BlockStack>
                </Card>
              </Layout.Section>
            )}
          </Layout>
        </BlockStack>
        
        {showToast && (
          <Toast
            content={toastMessage}
            onDismiss={() => setShowToast(false)}
          />
        )}
      </Page>
    </Frame>
  );
}
